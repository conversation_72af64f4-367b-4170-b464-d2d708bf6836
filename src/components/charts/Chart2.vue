<template>
  <div ref="chartRef" class="chart" style="width: 100%; height: 280px"></div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'Chart2',
  setup() {
    const chartRef = ref(null)
    let chartInstance = null

    const initChart = () => {
      if (!chartRef.value) return

      chartInstance = echarts.init(chartRef.value)
      
      const option = {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['销售额','订单数'],
          textStyle: {
            color: '#fff'
          },
          top: '8%'
        },
        grid: {
          top: '40%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        color: ['#FF4949','#FFA74D','#FFEA51','#4BF0FF','#44AFF0','#4E82FF','#584BFF','#BE4DFF','#F845F1'],
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['2018年9月','2018年10月','2018年11月','2018年12月','2019年1月'],
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        },
        yAxis: {
          name: '',
          type: 'value',
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        },
        series: [
          {
            name: '销售额',
            type: 'line',
            data: [3961.88, 4233.63, 4183.14, 3633.01, 3704.47]
          },
          {
            name: '订单数',
            type: 'line',
            data: [3374.76, 3364.76, 3274.76, 3371.82, 3259.87]
          }
        ]
      }

      chartInstance.setOption(option)
    }

    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef
    }
  }
}
</script>

<style scoped>
.chart {
  margin-top: 20px;
}
</style>
