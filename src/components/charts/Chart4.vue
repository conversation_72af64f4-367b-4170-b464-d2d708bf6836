<template>
  <div ref="chartRef" class="chart" style="width: 100%; height: 280px"></div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'Chart4',
  setup() {
    const chartRef = ref(null)
    let chartInstance = null

    // 模拟电商企业入驻数据
    const dianshangData = [120000, 80000, 60000, 45000, 30000, 25000]

    const initChart = () => {
      if (!chartRef.value) return

      chartInstance = echarts.init(chartRef.value)
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        toolbox: {
          show: false,
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        legend: {
          data: ['', ''],
          show: false
        },
        grid: {
          top: '18%',
          right: '5%',
          bottom: '8%',
          left: '5%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: [
            '名优企业',
            '技术服务',
            '技术开发',
            '电子商务',
            '网络推广',
            '涉互联网企业'
          ],
          splitLine: {
            show: false,
            lineStyle: {
              color: '#3c4452'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#fff'
            },
            lineStyle: {
              color: '#519cff'
            },
            alignWithLabel: true,
            interval: 0
          }
        }],
        yAxis: [{
          type: 'value',
          name: '入驻数据',
          nameTextStyle: {
            color: '#fff'
          },
          interval: 100000,
          min: 0,
          splitLine: {
            show: true,
            lineStyle: {
              color: '#23303f'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#115372'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#fff'
            },
            alignWithLabel: true,
            interval: 0
          }
        }],
        color: 'yellow',
        series: [{
          name: '电商企业入驻',
          type: 'bar',
          data: dianshangData,
          boundaryGap: '45%',
          barWidth: '40%',
          itemStyle: {
            normal: {
              color: function (params) {
                const colorList = [
                  '#6bc0fb',
                  '#7fec9d',
                  '#fedd8b',
                  '#ffa597',
                  '#84e4dd',
                  '#6bc0fb'
                ]
                return colorList[params.dataIndex]
              },
              label: {
                show: true,
                position: 'top',
                formatter: '{c}'
              }
            }
          }
        }]
      }

      chartInstance.setOption(option)
    }

    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef
    }
  }
}
</script>

<style scoped>
.chart {
  margin-top: 20px;
}
</style>
