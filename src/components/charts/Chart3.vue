<template>
  <div ref="chartRef" class="chart" style="width: 100%; height: 280px"></div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'Chart3',
  setup() {
    const chartRef = ref(null)
    let chartInstance = null

    const initChart = () => {
      if (!chartRef.value) return

      chartInstance = echarts.init(chartRef.value)
      
      const option = {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['顺丰快递','邮政速递','百世快递','圆通速递','中通速递','申通快递','韵达快递'],
          textStyle: {
            color: '#fff'
          },
          top: '8%'
        },
        grid: {
          top: '40%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        color: ['#FF4949','#FFA74D','#FFEA51','#4BF0FF','#44AFF0','#4E82FF','#584BFF','#BE4DFF','#F845F1'],
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['2018年9月','2018年10月','2018年11月','2018年12月','2019年1月'],
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        },
        yAxis: {
          name: '单',
          type: 'value',
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        },
        series: [
          {
            name: '顺丰快递',
            type: 'line',
            data: [3961, 4233, 4183, 3633, 3704]
          },
          {
            name: '邮政速递',
            type: 'line',
            data: [3374, 3364, 3274, 3371, 3259]
          },
          {
            name: '百世快递',
            type: 'line',
            data: [14, 15, 13, 14, 15]
          },
          {
            name: '圆通速递',
            type: 'line',
            data: [686, 847, 895, 865, 886]
          },
          {
            name: '中通速递',
            type: 'line',
            data: [6133, 6577, 7019, 6821, 7294]
          },
          {
            name: '申通快递',
            type: 'line',
            data: [509, 862, 1481, 1552, 1333]
          },
          {
            name: '韵达快递',
            type: 'line',
            data: [509, 900, 1350, 1487, 1600]
          }
        ]
      }

      chartInstance.setOption(option)
    }

    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef
    }
  }
}
</script>

<style scoped>
.chart {
  margin-top: 20px;
}
</style>
