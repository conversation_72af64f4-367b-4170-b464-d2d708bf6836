<template>
  <div class="dashboard">
    <!-- Header -->
    <div class="header">
      <div class="bg_header">
        <div class="header_nav fl t_title">
          xxxxxxxx
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="data_content">
      <div class="data_main">
        <!-- Left Column -->
        <div class="main_left fl">
          <!-- Chart 1 - 全区快递企业月寄递量 -->
          <div class="left_1 t_btn6" style="cursor: pointer">
            <BorderDecoration />
            <div class="main_title">
              <img src="/src/assets/images/t_1.png" alt="" />
              xxxxxxx
            </div>
            <Chart1 />
          </div>

          <!-- Chart 2 - 电子商务销售额、订单数 -->
          <div class="left_2" style="cursor: pointer">
            <BorderDecoration />
            <div class="main_title">
              <img src="/src/assets/images/t_2.png" alt="" />
              xxxxxxx
            </div>
            <Chart2 />
          </div>
        </div>

        <!-- Center Column -->
        <div class="main_center fl">
          <div class="center_text" style="position: relative">
            <BorderDecoration />
            <div class="main_title" style="width: 230px">
              <img src="/src/assets/images/t_3.png" alt="" />
              xxxxx
            </div>
            <!-- <MapChart /> -->
            <div class="linshi_zdy">
              <!-- 地图图例可以在这里添加 -->
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="main_right fr">
          <!-- Chart 3 - 全区快递月寄递数量 -->
          <div class="right_1">
            <BorderDecoration />
            <div class="main_title" style="width: 220px">
              <img src="/src/assets/images/t_4.png" alt="" />
              xxxxx
            </div>
            <Chart3 />
          </div>

          <!-- Chart 4 - 电商企业入驻情况 -->
          <div class="right_2">
            <BorderDecoration />
            <div class="main_title" style="width: 200px">
              <img src="/src/assets/images/t_5.png" alt="" />
              xxxxx
            </div>
            <Chart4 />
          </div>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="data_bottom">
        <!-- Bottom Left - 农村电商交易概况 -->
        <div class="bottom_1 fl t_btn5" style="cursor: pointer">
          <BorderDecoration />
          <div class="main_title">
            <img src="/src/assets/images/t_7.png" alt="" />
            xxxxx
          </div>
          <div class="main_table t_btn8">
            <table>
              <thead>
                <tr>
                  <th>概况名称</th>
                  <th>详情</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in tradeOverview" :key="item.name">
                  <td>{{ item.name }}</td>
                  <td>{{ item.value }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Bottom Center -->
        <div class="bottom_center fl">
          <!-- 热销产品排行榜 -->
          <div class="bottom_2 fl">
            <BorderDecoration />
            <div class="main_title" style="width: 300px">
              <img src="/src/assets/images/t_7.png" alt="" />
              xxxxxx
            </div>
            <div class="main_table t_btn8">
              <table>
                <thead>
                  <tr>
                    <th>产品名称</th>
                    <th>品种</th>
                    <th>产地</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="product in hotProducts" :key="product.name">
                    <td>{{ product.name }}</td>
                    <td>{{ product.category }}</td>
                    <td>{{ product.origin }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 热销店铺排行榜 -->
          <div class="bottom_3 fl">
            <BorderDecoration />
            <div class="main_title" style="width: 260px">
              <img src="/src/assets/images/t_7.png" alt="" />
              xxxx
            </div>
            <div class="main_table t_btn2">
              <table>
                <thead>
                  <tr>
                    <th>店铺名称</th>
                    <th>产品</th>
                    <th>销售额(月销)</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="shop in hotShops" :key="shop.name">
                    <td>{{ shop.name }}</td>
                    <td>{{ shop.product }}</td>
                    <td>{{ shop.sales }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Bottom Right - 平台活动案例 -->
        <div class="bottom_4 fr">
          <BorderDecoration />
          <div class="main_title">
            <img src="/src/assets/images/t_7.png" alt="" />
            xxxxx
          </div>
          <div class="main_table t_btn3 table_zdy">
            <table>
              <thead>
                <tr>
                  <th>活动主题</th>
                  <th>活动举办地</th>
                  <th>日期</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="activity in activities" :key="activity.theme">
                  <td>
                    <a v-if="activity.link" :href="activity.link" target="_blank">
                      {{ activity.theme }}
                    </a>
                    <span v-else>{{ activity.theme }}</span>
                  </td>
                  <td>{{ activity.location }}</td>
                  <td>{{ activity.date }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import BorderDecoration from './BorderDecoration.vue'
import Chart1 from './charts/Chart1.vue'
import Chart2 from './charts/Chart2.vue'
import Chart3 from './charts/Chart3.vue'
import Chart4 from './charts/Chart4.vue'
import MapChart from './charts/MapChart.vue'

export default {
  name: 'Dashboard',
  components: {
    BorderDecoration,
    Chart1,
    Chart2,
    Chart3,
    Chart4,
    MapChart
  },
  setup() {
    // 农村电商交易概况数据
    const tradeOverview = ref([
      { name: '累计交易总金额', value: '4058.56 万元' },
      { name: '累计交易订单数量', value: '437753 件' },
      { name: '累计产品SKU数量', value: '360 个' },
      { name: '本月交易总额', value: '242.42 万元' },
      { name: '本月交易订单数量', value: '5283 件' }
    ])

    // 热销产品数据
    const hotProducts = ref([
      { name: '荔浦百香果', category: '百香果', origin: '荔浦' },
      { name: '荔浦砂糖桔', category: '砂糖桔', origin: '荔浦' },
      { name: '90g将军峰小方盒绿茶', category: '中小叶种', origin: '广西贺州' },
      { name: '珍珠糯玉米', category: '粮食', origin: '忻城县' },
      { name: '桂花红糖', category: '桂花红糖', origin: '大新县' }
    ])

    // 热销店铺数据
    const hotShops = ref([
      { name: '鲜迪食品专营店', product: '海鸭蛋', sales: '2.8万' },
      { name: '中鼎水果专营店', product: '红心芭乐番石榴', sales: '2.5万' },
      { name: '中闽飘香旗舰店', product: '广西桂林罗汉果', sales: '2.4万' },
      { name: '芋小妹旗舰店', product: '广西荔浦大芋头', sales: '1.3万' },
      { name: '桂甄堂旗舰店', product: '柳州螺狮粉', sales: '1.1万' }
    ])

    // 平台活动案例数据
    const activities = ref([
      {
        theme: '2018广西特产行销全国',
        location: '南宁',
        date: '2018年',
        link: 'http://www.gxitps.org/zhanhui/detail/id/20.html'
      },
      {
        theme: '2018壮族三月三电商节',
        location: '南宁',
        date: '2018年',
        link: 'http://www.gxitps.org/zhanhui/detail/id/16.html'
      },
      {
        theme: '2018灵山荔枝节',
        location: '灵山县',
        date: '2018年',
        link: 'http://www.gxitps.org/zhanhui/detail/id/17.html'
      },
      { theme: '2018年货节', location: '广西', date: '2018年' },
      { theme: '2017好讲师大赛', location: '南宁', date: '2017年' }
    ])

    return {
      tradeOverview,
      hotProducts,
      hotShops,
      activities
    }
  }
}
</script>

<style scoped>
.dashboard {
  width: 100vw;
  height: 100vh;
  background: url('/src/assets/images/bg.jpg') no-repeat;
  background-size: 100% 100%;
  overflow: hidden; /* 防止出现滚动条 */
  display: flex;
  flex-direction: column;
}

/* Header样式 */
.header {
  width: 100%;
  height: 8vh; /* 使用视口高度单位 */
  flex-shrink: 0; /* 防止头部被压缩 */
}

.bg_header {
  width: 100%;
  height: 100%;
  background: url('/src/assets/images/title.png') no-repeat;
  background-size: 100% 100%;
}

/* Dashboard样式 - 自适应布局 */
.data_content {
  flex: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  padding: 1vh 1vw; /* 使用视口单位 */
  box-sizing: border-box;
  overflow: hidden;
}

/* Main layout */
.data_main {
  flex: 1; /* 占据可用空间 */
  display: flex;
  align-items: stretch;
  gap: 1vw; /* 使用视口宽度单位作为间距 */
  margin-bottom: 1vh;
}

.main_left {
  flex: 0 0 30%; /* 固定比例，不缩放 */
  display: flex;
  flex-direction: column;
  gap: 1vh; /* 内部元素间距 */
}

.main_center {
  flex: 0 0 36%;
  display: flex;
  flex-direction: column;
}

.main_right {
  flex: 0 0 30%;
  display: flex;
  flex-direction: column;
  gap: 1vh;
}

/* Chart containers */
.left_1, .left_2, .right_1, .right_2, .center_text {
  position: relative;
  background: rgba(6, 30, 93, 0.5);
  border: 1px solid rgba(25, 186, 139, 0.17);
  padding: 4vh 1.5vw 1.5vh 1.5vw; /* 为标题留出足够空间 */
  box-sizing: border-box;
  overflow: hidden;
}

/* 左右列的容器各占一半高度 */
.left_1, .left_2, .right_1, .right_2 {
  flex: 1; /* 平分可用空间 */
}

/* 中间容器填充整个列高度 */
.center_text {
  flex: 1;
}

.main_title {
  width: min(245px, 80%); /* 响应式宽度 */
  height: 3.5vh; /* 使用视口高度 */
  line-height: 3.3vh;
  background-color: #2C58A6;
  border-radius: 18px;
  position: absolute;
  top: 0; /* 使用正值，确保完全显示 */
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: clamp(14px, 1.2vw, 18px); /* 响应式字体 */
  font-weight: 600;
  box-sizing: border-box;
  padding-left: 3vw;
  z-index: 999999;
  text-align: center;
}

.main_title img {
  position: absolute;
  top: 0.8vh;
  left: 1.5vw;
  width: auto;
  height: 60%; /* 相对于标题高度 */
}

/* Bottom section */
.data_bottom {
  flex: 0 0 auto; /* 不拉伸，保持内容高度 */
  display: flex;
  gap: 1vw;
  height: 35vh; /* 固定底部区域高度 */
}

.bottom_1 {
  flex: 0 0 25%;
}

.bottom_center {
  flex: 0 0 48%;
  display: flex;
  gap: 1vw;
}

.bottom_2, .bottom_3 {
  flex: 1; /* 平分 bottom_center 的空间 */
}

.bottom_4 {
  flex: 0 0 25%;
}

.bottom_1, .bottom_2, .bottom_3, .bottom_4 {
  position: relative;
  background: rgba(6, 30, 93, 0.5);
  border: 1px solid rgba(25, 186, 139, 0.17);
  padding: 4vh 1.5vw 1.5vh 1.5vw; /* 为标题留出足够空间 */
  box-sizing: border-box;
  overflow: hidden;
}

/* Table styles */
.main_table {
  margin-top: 1vh; /* 减少margin-top，因为容器已有足够的顶部padding */
  height: calc(100% - 5vh); /* 调整高度计算 */
  overflow-y: auto;
}

.main_table table {
  width: 100%;
  border-collapse: collapse;
  color: #fff;
  font-size: clamp(10px, 0.8vw, 14px); /* 响应式字体 */
}

.main_table th {
  background: rgba(44, 88, 166, 0.8);
  padding: 0.8vh 0.5vw;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.main_table td {
  padding: 0.6vh 0.5vw;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.3);
}

.main_table a {
  color: #4bf0ff;
  text-decoration: none;
}

.main_table a:hover {
  text-decoration: underline;
}

/* 地图图例样式 */
.linshi_zdy {
  position: absolute;
  right: 1vw;
  bottom: 5vh; /* 使用底部定位 */
}

.linshi_zdy li {
  width: 10vw;
  font-size: clamp(12px, 1vw, 16px);
  padding: 0.3vh 0.8vw;
  cursor: pointer;
}

.linshi_zdy span {
  display: block;
  width: 1vw;
  height: 1vw;
  float: left;
  border-radius: 50%;
  margin-top: 0.3vh;
  margin-right: 0.5vw;
}

.linshi_zdy li:first-child {
  color: #ff0000;
}

.linshi_zdy li:first-child span {
  background: #ff0000;
}

.linshi_zdy li:nth-child(2) {
  color: #9cff00;
}

.linshi_zdy li:nth-child(2) span {
  background: #9cff00;
}

.linshi_zdy li:nth-child(3) {
  color: #fff;
}

.linshi_zdy li:nth-child(3) span {
  background: #fff;
}

.linshi_zdy li:nth-child(4) {
  color: #f4a100;
}

.linshi_zdy li:nth-child(4) span {
  background: #f4a100;
}
</style>
