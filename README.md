# 广西电子商务公共服务平台大数据中心 - Vue3版本

这是将原始HTML页面转换为Vue3应用的版本，保持了原有的视觉效果和功能。

## 功能特性

- 📊 **多种图表展示**: 饼图、折线图、柱状图、地图等
- 🎨 **科技感UI**: 保持原有的蓝色科技风格
- 📱 **响应式设计**: 支持不同屏幕尺寸
- ⚡ **Vue3 + Vite**: 现代化的开发体验
- 📈 **ECharts集成**: 丰富的数据可视化

## 项目结构

```
vue3-dashboard/
├── src/
│   ├── components/
│   │   ├── Dashboard.vue          # 主仪表板组件
│   │   ├── BorderDecoration.vue   # 边框装饰组件
│   │   └── charts/                # 图表组件目录
│   │       ├── Chart1.vue         # 全区快递企业月寄递量（饼图）
│   │       ├── Chart2.vue         # 电子商务销售额、订单数（折线图）
│   │       ├── Chart3.vue         # 全区快递月寄递数量（折线图）
│   │       ├── Chart4.vue         # 电商企业入驻情况（柱状图）
│   │       └── MapChart.vue       # 广西电子商务进农村（地图）
│   ├── assets/
│   │   └── styles/
│   │       └── global.css         # 全局样式
│   ├── App.vue                    # 根组件
│   └── main.js                    # 入口文件
├── package.json
├── vite.config.js
└── index.html
```

## 安装和运行

### 1. 安装依赖

```bash
cd vue3-dashboard
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

项目将在 `http://localhost:3000` 启动

### 3. 构建生产版本

```bash
npm run build
```

## 主要组件说明

### Dashboard.vue
主仪表板组件，包含：
- 页面头部标题
- 左侧图表区域（Chart1, Chart2）
- 中央地图区域（MapChart）
- 右侧图表区域（Chart3, Chart4）
- 底部数据表格区域

### 图表组件
- **Chart1**: 全区快递企业月寄递量饼图
- **Chart2**: 电子商务销售额、订单数折线图
- **Chart3**: 全区快递月寄递数量多线图
- **Chart4**: 电商企业入驻情况柱状图
- **MapChart**: 广西电子商务进农村地图（散点图）

### BorderDecoration.vue
可复用的边框装饰组件，为各个图表容器添加科技感边框效果。

## 技术栈

- **Vue 3**: 使用Composition API
- **Vite**: 快速的构建工具
- **ECharts 5**: 数据可视化图表库
- **CSS3**: 样式和动画效果

## 数据说明

当前版本使用模拟数据，在实际部署时需要：

1. 替换各图表组件中的静态数据为API调用
2. 添加数据更新机制
3. 处理数据加载状态和错误处理

## 样式特性

- 保持原有的深蓝色科技风格
- 半透明背景效果
- 发光边框装饰
- 响应式布局
- 图表自适应尺寸

## 浏览器兼容性

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 开发说明

### 添加新图表
1. 在 `src/components/charts/` 目录下创建新的图表组件
2. 在 `Dashboard.vue` 中引入并使用
3. 配置相应的ECharts选项

### 修改样式
- 全局样式在 `src/assets/styles/global.css`
- 组件样式使用scoped CSS
- 保持原有的颜色主题和布局

### 数据集成
- 在各图表组件的 `setup()` 函数中添加数据获取逻辑
- 使用Vue3的响应式数据管理
- 考虑添加loading状态和错误处理

## 注意事项

1. 地图组件需要广西地图的GeoJSON数据才能正常显示地图轮廓
2. 图片资源需要放置在 `public/src/assets/images/` 目录下
3. 背景图片路径需要根据实际部署环境调整

## 许可证

本项目基于原始HTML页面转换而来，请遵循相应的使用条款。
