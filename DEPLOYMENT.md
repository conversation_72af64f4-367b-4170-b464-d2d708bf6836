# 部署说明

## 开发环境启动

### 方法1: 使用脚本启动（推荐）

**Windows用户:**
```bash
双击运行 start.bat
```

**Linux/Mac用户:**
```bash
chmod +x start.sh
./start.sh
```

### 方法2: 手动启动

```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev

# 3. 访问 http://localhost:3000
```

## 生产环境部署

### 1. 构建项目

```bash
npm run build
```

构建完成后，`dist` 目录包含所有静态文件。

### 2. 部署到Web服务器

将 `dist` 目录的内容上传到Web服务器（如Nginx、Apache等）。

### 3. Nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

## 注意事项

### 1. 图片资源
- 将原项目的图片文件复制到 `public/src/assets/images/` 目录
- 主要包括：t_1.png, t_2.png, t_3.png, t_4.png, t_5.png, t_7.png, title.png, bg.jpg

### 2. 地图数据
- MapChart组件需要广西地图的GeoJSON数据
- 可以从以下来源获取：
  - ECharts官方地图数据
  - 阿里云DataV地图数据
  - 自定义GeoJSON数据

### 3. API集成
- 当前使用模拟数据
- 生产环境需要替换为真实API调用
- 建议在 `src/api/` 目录下创建API服务模块

### 4. 性能优化
- 启用gzip压缩
- 配置CDN加速
- 图片资源优化
- 代码分割和懒加载

## 常见问题

### Q: 图表不显示？
A: 检查ECharts是否正确安装，确保容器有正确的宽高。

### Q: 地图显示空白？
A: 需要注册广西地图数据，参考MapChart.vue组件注释。

### Q: 样式错乱？
A: 检查CSS文件路径，确保全局样式正确加载。

### Q: 开发服务器启动失败？
A: 检查Node.js版本，确保端口3000未被占用。

## 技术支持

如有问题，请检查：
1. 浏览器控制台错误信息
2. 网络请求状态
3. 组件渲染状态
4. ECharts配置选项
