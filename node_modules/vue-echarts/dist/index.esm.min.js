import{watch as e,isRef as t,unref as n,inject as r,computed as i,watchEffect as o,Vue2 as a,defineComponent as u,shallowRef as s,toRefs as c,getCurrentInstance as l,onMounted as f,onBeforeUnmount as p,h as v,nextTick as d}from"vue-demi";import{throttle as h,init as g}from"echarts/core";import{addListener as m,removeListener as O}from"resize-detector";var b=function(){return b=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},b.apply(this,arguments)};"function"==typeof SuppressedError&&SuppressedError;var y=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function E(e){return t=Object.create(null),y.forEach((function(n){t[n]=function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[t].apply(e.value,n)}}(n)})),t;var t}var _={autoresize:[Boolean,Object]},x=/^on[^a-z]/,j=function(e){return x.test(e)};function w(e,r){var i=t(e)?n(e):e;return i&&"object"==typeof i&&"value"in i?i.value||r:i||r}var A="ecLoadingOptions";var L={loading:Boolean,loadingOptions:Object},z=null,C="x-vue-echarts";var T=[],S=[];!function(e,t){if(e&&"undefined"!=typeof document){var n,r=!0===t.prepend?"prepend":"append",i=!0===t.singleTag,o="string"==typeof t.container?document.querySelector(t.container):document.getElementsByTagName("head")[0];if(i){var a=T.indexOf(o);-1===a&&(a=T.push(o)-1,S[a]={}),n=S[a]&&S[a][r]?S[a][r]:S[a][r]=u()}else n=u();65279===e.charCodeAt(0)&&(e=e.substring(1)),n.styleSheet?n.styleSheet.cssText+=e:n.appendChild(document.createTextNode(e))}function u(){var e=document.createElement("style");if(e.setAttribute("type","text/css"),t.attributes)for(var n=Object.keys(t.attributes),i=0;i<n.length;i++)e.setAttribute(n[i],t.attributes[n[i]]);var a="prepend"===r?"afterbegin":"beforeend";return o.insertAdjacentElement(a,e),e}}("x-vue-echarts{display:flex;flex-direction:column;width:100%;height:100%;min-width:0}\n.vue-echarts-inner{flex-grow:1;min-width:0;width:auto!important;height:auto!important}\n",{});var U=function(){if(null!=z)return z;if("undefined"==typeof HTMLElement||"undefined"==typeof customElements)return z=!1;try{new Function("tag","class EChartsElement extends HTMLElement {\n  __dispose = null;\n\n  disconnectedCallback() {\n    if (this.__dispose) {\n      this.__dispose();\n      this.__dispose = null;\n    }\n  }\n}\n\nif (customElements.get(tag) == null) {\n  customElements.define(tag, EChartsElement);\n}\n")(C)}catch(e){return z=!1}return z=!0}();a&&a.config.ignoredElements.push(C);var D="ecTheme",k="ecInitOptions",B="ecUpdateOptions",P=/(^&?~?!?)native:/,H=u({name:"echarts",props:b(b({option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean},_),L),emits:{},inheritAttrs:!1,setup:function(t,n){var a=n.attrs,u=s(),v=s(),y=s(),_=s(),x=r(D,null),L=r(k,null),z=r(B,null),C=c(t),T=C.autoresize,S=C.manualUpdate,H=C.loading,M=C.loadingOptions,R=i((function(){return _.value||t.option||null})),F=i((function(){return t.theme||w(x,{})})),N=i((function(){return t.initOptions||w(L,{})})),$=i((function(){return t.updateOptions||w(z,{})})),q=i((function(){return function(e){var t={};for(var n in e)j(n)||(t[n]=e[n]);return t}(a)})),I={},W=l().proxy.$listeners,Z={};function G(e){if(v.value){var n=y.value=g(v.value,F.value,N.value);t.group&&(n.group=t.group),Object.keys(Z).forEach((function(e){var t=Z[e];if(t){var r=e.toLowerCase();"~"===r.charAt(0)&&(r=r.substring(1),t.__once__=!0);var i=n;if(0===r.indexOf("zr:")&&(i=n.getZr(),r=r.substring(3)),t.__once__){delete t.__once__;var o=t;t=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];o.apply(void 0,e),i.off(r,t)}}i.on(r,t)}})),T.value?d((function(){n&&!n.isDisposed()&&n.resize(),r()})):r()}function r(){var t=e||R.value;t&&n.setOption(t,$.value)}}function J(){y.value&&(y.value.dispose(),y.value=void 0)}W?Object.keys(W).forEach((function(e){P.test(e)?I[e.replace(P,"$1")]=W[e]:Z[e]=W[e]})):Object.keys(a).filter((function(e){return j(e)})).forEach((function(e){var t=e.charAt(2).toLowerCase()+e.slice(3);if(0!==t.indexOf("native:"))"Once"===t.substring(t.length-4)&&(t="~".concat(t.substring(0,t.length-4))),Z[t]=a[e];else{var n="on".concat(t.charAt(7).toUpperCase()).concat(t.slice(8));I[n]=a[e]}}));var K=null;e(S,(function(n){"function"==typeof K&&(K(),K=null),n||(K=e((function(){return t.option}),(function(e,t){e&&(y.value?y.value.setOption(e,b({notMerge:e!==t},$.value)):G())}),{deep:!0}))}),{immediate:!0}),e([F,N],(function(){J(),G()}),{deep:!0}),o((function(){t.group&&y.value&&(y.value.group=t.group)}));var Q=E(y);return function(e,t,n){var a=r(A,{}),u=i((function(){return b(b({},w(a,{})),null==n?void 0:n.value)}));o((function(){var n=e.value;n&&(t.value?n.showLoading(u.value):n.hideLoading())}))}(y,H,M),function(t,n,r){var i=null;e([r,t,n],(function(e,t,n){var r=e[0],o=e[1],a=e[2];if(r&&o&&a){var u=!0===a?{}:a,s=u.throttle,c=void 0===s?100:s,l=u.onResize,f=function(){o.resize(),null==l||l()};i=c?h(f,c):f,m(r,i)}n((function(){r&&i&&O(r,i)}))}))}(y,T,v),f((function(){G()})),p((function(){U&&u.value?u.value.__dispose=J:J()})),b({chart:y,root:u,inner:v,setOption:function(e,n){t.manualUpdate&&(_.value=e),y.value?y.value.setOption(e,n||{}):G(e)},nonEventAttrs:q,nativeListeners:I},Q)},render:function(){var e=a?{attrs:this.nonEventAttrs,on:this.nativeListeners}:b(b({},this.nonEventAttrs),this.nativeListeners);return e.ref="root",e.class=e.class?["echarts"].concat(e.class):"echarts",v(C,e,[v("div",{ref:"inner",class:"vue-echarts-inner"})])}});export{k as INIT_OPTIONS_KEY,A as LOADING_OPTIONS_KEY,D as THEME_KEY,B as UPDATE_OPTIONS_KEY,H as default};
//# sourceMappingURL=index.esm.min.js.map
